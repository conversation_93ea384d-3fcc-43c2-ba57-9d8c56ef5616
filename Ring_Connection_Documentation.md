# Ring Connection - Simple Guide

## What is Ring Connection?
The app connects to a smart ring device via Bluetooth to collect health data like heart rate, steps, sleep, and battery level.

---

## iOS Implementation

### How it Works
- Uses **YCProductSDK** (main) + **BleServiceV2** (backup)
- Connects via Bluetooth Low Energy (BLE)
- Sends commands and receives health data

### Key Files
- `ServiceBle.swift` - Main connection service
- `BleServiceV2.swift` - Backup connection service
- `AppDelegate.swift` - Connects to Flutter

### Bluetooth Settings
**Service UUID:** `FFF0` (main communication channel)
**Send Data:** `FFF6` (app → ring)
**Receive Data:** `FFF7` (ring → app)

### Required Permissions (Info.plist)
```xml
<key>NSBluetoothAlwaysUsageDescription</key>
<string>App uses Bluetooth to connect to ring device</string>

<key>UIBackgroundModes</key>
<array>
    <string>bluetooth-central</string>
</array>
```

### What SDK Does
- **YCProductSDK.framework** - Handles ring communication
- **BleSDK_J2301A** - Converts ring data to readable format

### Special Features Added
1. **Command Queue** - Sends one command at a time to avoid conflicts
2. **Auto Retry** - Tries to reconnect if connection fails
3. **Dual Mode** - Uses backup service if main SDK fails

---

## Android Implementation

### How it Works
- Uses custom **BleService.java** (no external SDK)
- Connects via Android Bluetooth APIs
- Handles scanning, connecting, and data exchange

### Key Files
- `BleService.java` - Main connection service
- `BleManager.java` - Device scanning
- `MainActivity.java` - Connects to Flutter

### Bluetooth Settings
**Service UUID:** `0000fff0-0000-1000-8000-00805f9b34fb`
**Send Data:** `0000fff6-0000-1000-8000-00805f9b34fb` (app → ring)
**Receive Data:** `0000fff7-0000-1000-8000-00805f9b34fb` (ring → app)

### Required Permissions (AndroidManifest.xml)
```xml
<uses-permission android:name="android.permission.BLUETOOTH" />
<uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
<uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/>
<uses-feature android:name="android.hardware.bluetooth_le" android:required="true" />
```

### Special Features Added
1. **Priority Connection** - Faster connection for already paired devices
2. **Smart Scanning** - Only shows ring devices, filters out others
3. **Queue System** - Sends data one by one to prevent errors
4. **Auto Retry** - Tries multiple times if connection fails
5. **API Support** - Works on old and new Android versions

---

## How Flutter App Uses Ring Connection

### Available Commands
- `connectToDeviceV2` - Connect to ring
- `disconnectToDeviceV2` - Disconnect from ring
- `isDeviceConnectedV2` - Check if connected
- `startScanDevicesV2` - Find nearby rings
- `getBatteryLevelV2` - Get ring battery level

### What Data We Get
- **Health Data**: Heart rate, steps, sleep, SpO2
- **Device Info**: Battery level, firmware version, MAC address
- **Connection Status**: Connected/disconnected state

---

## Quick Comparison

| Feature | iOS | Android |
|---------|-----|---------|
| **Main SDK** | YCProductSDK | Custom BLE Service |
| **Backup** | BleServiceV2 | None |
| **Permissions** | Info.plist | Runtime requests |
| **Connection** | YCProduct.connectDevice() | BluetoothGatt.connectGatt() |

---

## Common Problems & Solutions

### Connection Issues
- **Problem**: Ring won't connect
- **Solution**: Check Bluetooth is on, ring is nearby, permissions granted

### Data Not Updating
- **Problem**: Health data not syncing
- **Solution**: Ensure ring is connected, check notification setup

### Battery Drain
- **Problem**: App drains phone battery
- **Solution**: Optimize scan intervals, disconnect when not needed

---

## Debug Tips
- Look for "RING DEBUG" in logs
- Check Bluetooth permissions first
- Verify ring is in pairing mode
- Test with different ring devices

## Detailed Implementation Flow

### iOS Connection Flow
1. **Initialization**: `initializeYCProduct()` sets up YCProductSDK
2. **Scanning**: `YCProduct.scanningDevice()` discovers nearby devices
3. **Connection**: `YCProduct.connectDevice()` establishes connection
4. **Data Exchange**: Command queue processes requests sequentially
5. **Parsing**: `BleSDK_J2301A.DataParsingWithData()` processes responses

### Android Connection Flow
1. **Initialization**: `BleManager` and `BleService` setup
2. **Scanning**: `BluetoothLeScanner.startScan()` with filtering
3. **Connection**: `BluetoothGatt.connectGatt()` with priority handling
4. **Service Discovery**: Discover services and characteristics
5. **Notification Setup**: Enable notifications for data reception
6. **Data Exchange**: Queue-based write operations with callbacks

## Data Types Supported

### Health Metrics
- **Heart Rate**: Real-time and historical data
- **SpO2**: Blood oxygen saturation levels
- **Steps**: Daily step count and activity
- **Sleep**: Sleep stages and duration
- **Battery**: Device battery level and status
- **Temperature**: Body temperature readings

### Device Information
- **MAC Address**: Unique device identifier
- **Firmware Version**: Device software version
- **Device Model**: Hardware model information
- **Battery Status**: Charging state and level
- **Connection State**: Current connection status

## Flutter Integration Details

### Method Channel Implementation
```dart
// Native Communicator Service
class NativeCommunicator {
  static const _platform = MethodChannel('saiwell_native');

  Future<void> connectToDeviceV2({
    required String macId,
    required void Function()? updateRingStatusToWebView,
    required void Function() reloadWebView,
    required void Function() updateDeviceConnectionDetailsToWebview,
    required void Function() updateBetteryStatusInWebView,
  }) async {
    final result = await _platform.invokeMethod('connectToDeviceV2', macId);
    // Handle connection result and update UI
  }
}
```

### State Management
- **Global Controller**: Manages app-wide ring connection state
- **Ring Controller**: Handles ring-specific operations
- **Connection Scheduler**: Automatic reconnection and monitoring

## Security Considerations

### Data Encryption
- BLE communication uses standard encryption
- Device pairing provides additional security layer
- Data transmission follows healthcare privacy standards

### Permission Handling
- Runtime permission requests on Android
- Graceful degradation when permissions denied
- User-friendly permission explanation dialogs

## Performance Optimizations

### Connection Optimization
- **Fast Connect Mode**: Priority connections for bonded devices
- **Auto Connect**: Automatic reconnection for known devices
- **Connection Pooling**: Reuse existing connections when possible

### Data Optimization
- **Command Queuing**: Sequential command execution prevents conflicts
- **Timeout Management**: Prevents hanging operations
- **Background Processing**: Efficient data parsing and storage

### Battery Optimization
- **Scan Intervals**: Optimized scanning frequency
- **Connection Parameters**: Balanced for performance and battery life
- **Background Modes**: Minimal background activity

## Testing & Validation

### Unit Tests
- Connection state management
- Data parsing accuracy
- Error handling scenarios

### Integration Tests
- End-to-end connection flow
- Data synchronization validation
- Permission handling verification

### Device Compatibility
- Multiple ring device models supported
- Android API level compatibility (21+)
- iOS version compatibility (12.0+)

## Maintenance & Updates

### SDK Updates
- Regular YCProductSDK updates for iOS
- BleSDK updates for data parsing
- Compatibility testing with new versions

### Bug Fixes
- Connection stability improvements
- Data accuracy enhancements
- Performance optimizations

### Feature Additions
- New health metrics support
- Enhanced error reporting
- Improved user experience

## Support & Documentation

### Developer Resources
- SDK documentation and examples
- API reference guides
- Best practices documentation

### Troubleshooting Guides
- Common connection issues
- Data synchronization problems
- Performance optimization tips
