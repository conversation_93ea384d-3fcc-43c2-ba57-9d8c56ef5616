# Ring Connection - Complete Handover Documentation

## Overview
The Ring Connection feature enables the Flutter app to connect to smart ring devices via Bluetooth Low Energy (BLE) to collect health data including heart rate, steps, sleep patterns, SpO2, and battery information.

---

## iOS Implementation

### How It Works
- **Current SDK**: BleSDK_J2301A (only SDK in use - v2 methods)
- **Implementation**: BleServiceV2.swift (custom BLE service using BleSDK_J2301A)
- **Connection Method**: Bluetooth Low Energy (BLE) with command queue management
- **Architecture**: Single SDK approach with robust command handling

### Key Components

#### 1. BleServiceV2 (Current Implementation)
- **Location**: `ios/Runner/BleServiceV2.swift`
- **SDK Used**: BleSDK_J2301A.sharedManager()
- **Purpose**: Complete BLE implementation for Ring connection
- **Features**: Command queue, timeout handling, data parsing, connection management

#### 2. Legacy Components (Not Used)
- **YCProductSDK**: Old SDK - methods without v2 suffix are deprecated
- **ServiceBleiOS**: Old implementation - replaced by BleServiceV2

### BLE Characteristics & Services

#### Service UUID
- **Primary Service**: `FFF0`

#### Characteristics
- **Send Data (App → Ring)**: `FFF6`
- **Receive Data (Ring → App)**: `FFF7`
- **Notification Descriptor**: `2902`

### BleSDK_J2301A Methods (Current v2 Implementation)

#### Connection Methods
```swift
// Connect to device (v2 method)
func connectToDevice(uuidString: String, completion: @escaping (Bool, Error?) -> Void)

// Disconnect device (v2 method)
func disconnectToDevice(completion: ((Bool, Error?) -> Void)?)

// Check connection status (v2 method)
func isConnected() -> Bool

// Start scanning (v2 method)
func startScanning(completion: @escaping ([CBPeripheral]?, Error?) -> Void)
```

#### Data Retrieval Methods (Using BleSDK_J2301A)
```swift
// Battery level
bleSDK.getDeviceBatteryLevel() -> NSMutableData

// Device information
bleSDK.getDeviceVersion() -> NSMutableData
bleSDK.getDeviceMacAddress() -> NSMutableData
bleSDK.getDeviceTime() -> NSMutableData

// Health data
bleSDK.getHRVData(withMode: 0, withStart: nil) -> NSMutableData
bleSDK.getDetailSleepData(withMode: 0, withStart: nil) -> NSMutableData
bleSDK.getAutomaticSpo2Data(withMode: 0, withStart: nil) -> NSMutableData
bleSDK.getDetailActivityData(withMode: 0, withStart: nil) -> NSMutableData
bleSDK.getTemperatureData(withMode: 0, withStart: nil) -> NSMutableData

// PPG monitoring
bleSDK.ppg(withMode: 1, ppgStatus: 0) -> NSMutableData // Start
bleSDK.ppg(withMode: 3, ppgStatus: 0) -> NSMutableData // Stop

// Data parsing
bleSDK.dataParsing(with: Data) -> DeviceData?
```

### Permissions Required (Info.plist)
```xml
<key>NSBluetoothAlwaysUsageDescription</key>
<string>App uses Bluetooth to connect to ring device for health monitoring</string>

<key>UIBackgroundModes</key>
<array>
    <string>bluetooth-central</string>
</array>
```

### Special Features Added (v2 Implementation)

#### 1. Command Queue System
- **Purpose**: Prevents command conflicts by processing one command at a time
- **Implementation**: Queue-based command processing with 15-second timeout
- **Location**: `BleServiceV2.swift` lines 65-67
- **SDK Integration**: Uses BleSDK_J2301A for all data operations

#### 2. Automatic Retry Logic
- **Purpose**: Handles connection failures gracefully
- **Implementation**: Automatic reconnection attempts with exponential backoff
- **Location**: `RingConnectivity.swift`

#### 3. Data Pagination
- **Purpose**: Handles large data sets (activity, sleep data)
- **Implementation**: Automatic next-page requests when data count reaches 50
- **Location**: `BleServiceV2.swift` lines 606-624
- **SDK Method**: Uses BleSDK_J2301A pagination modes

#### 4. Real-time Data Parsing
- **Purpose**: Converts raw BLE data to readable format
- **Implementation**: BleSDK_J2301A.dataParsing() method
- **Location**: `BleServiceV2.swift` handleReceivedData method

---

## Android Implementation

### How It Works
- **Current SDK**: blesdk2301 (only SDK in use - v2 methods)
- **Implementation**: Custom BLE service with direct GATT operations
- **Connection Method**: BluetoothGatt with priority optimization
- **Architecture**: Service-based with command queue and priority handling

### Key Components

#### 1. BleManager
- **Location**: `android/app/src/main/java/com/saigeware/sh/saiwell/BleManager.java`
- **Purpose**: Main Bluetooth management and device scanning
- **Key Features**: Priority connection, device filtering, scan optimization

#### 2. BleService
- **Location**: `android/app/src/main/java/com/saigeware/sh/saiwell/BleService.java`
- **Purpose**: Low-level GATT operations and data handling
- **Key Features**: Connection management, characteristic notifications

#### 3. MainActivity
- **Location**: `android/app/src/main/java/com/saigeware/sh/saiwell/MainActivity.java`
- **Purpose**: Flutter method channel bridge and blesdk2301 integration
- **Key Features**: Command queue, data accumulation, PPG streaming, v2 method handlers

### BLE Characteristics & Services

#### Service UUID
- **Primary Service**: `0000fff0-0000-1000-8000-00805f9b34fb`

#### Characteristics
- **Send Data**: `0000fff6-0000-1000-8000-00805f9b34fb`
- **Receive Data**: `0000fff7-0000-1000-8000-00805f9b34fb`
- **Notification Descriptor**: `00002902-0000-1000-8000-00805f9b34fb`

### blesdk2301 Methods (Current v2 Implementation)

#### Connection Methods (v2 - Current Implementation)
```java
// Connect to device (uses blesdk2301)
connectToDeviceV2(String macId, Result result)

// Disconnect device (uses blesdk2301)
disconnectToDeviceV2(Result result)

// Check connection status (uses blesdk2301)
isDeviceConnectedV2(Result result)

// Start scanning (uses blesdk2301)
startScanDevicesV2(Result result)
```

#### Data Retrieval Methods (v2 - Using blesdk2301)
```java
// Battery level
getBatteryLevelV2(Result result)

// Health data (all use blesdk2301 parsing)
getHRVDataWithModeV2(Result result)
getSleepDataV2(Result result)
getBloodOxygenV2(Result result)
getTotalActivityDataWithModeV2(Result result)
getTemperatureDataV2(Result result)

// PPG monitoring
startPPGWaveV2(Result result)
stopPPGWaveV2(Result result)
```

#### Why v2 Suffix?
- **v2 methods**: Use current blesdk2301 SDK
- **Non-v2 methods**: Old deprecated methods (not in use)
- **Purpose**: Clear distinction between old and current implementation

### Permissions Required (AndroidManifest.xml)
```xml
<!-- Core Bluetooth permissions -->
<uses-permission android:name="android.permission.BLUETOOTH" />
<uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
<uses-permission android:name="android.permission.BLUETOOTH_SCAN" android:usesPermissionFlags="neverForLocation" />
<uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />

<!-- Location permissions (required for BLE scanning) -->
<uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"/>
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/>

<!-- Hardware feature -->
<uses-feature android:name="android.hardware.bluetooth_le" android:required="true" />
```

### Special Features Added (v2 Implementation)

#### 1. Priority Connection System
- **Purpose**: Faster connection for bonded devices
- **Implementation**: Uses `TRANSPORT_LE` for API 23+ with bonded device detection
- **Location**: `BleService.java` lines 124-162
- **SDK Integration**: Works with blesdk2301 data parsing

#### 2. Smart Device Filtering
- **Purpose**: Only shows ring devices (filters by name "2301")
- **Implementation**: Device name filtering during scan
- **Location**: `BleManager.java` lines 376-390
- **SDK Integration**: Compatible with blesdk2301 device identification

#### 3. Command Queue System (v2)
- **Purpose**: Prevents data conflicts by queuing v2 commands
- **Implementation**: HashMap-based pending results for v2 methods
- **Location**: `MainActivity.java` lines 51-53

#### 4. Auto-Retry Logic
- **Purpose**: Multiple connection attempts on failure
- **Implementation**: Retry counter with connection state management
- **Location**: `MainActivity.java` connection callbacks

#### 5. API Level Compatibility
- **Purpose**: Works on Android 5.0+ (API 21+) with optimizations for newer versions
- **Implementation**: Version-specific scan settings and connection methods
- **Location**: `BleManager.java` lines 254-267
- **SDK Support**: blesdk2301 compatible across all supported API levels

---

## Flutter Integration

### Method Channel
- **Channel Name**: `saiwell_native`
- **Implementation**: `lib/services/native_communicator.dart`

### Available v2 Methods (Current Implementation)
```dart
// Connection (v2 methods using current SDKs)
Future<void> connectToDeviceV2({required String macId, ...})
Future<void> disconnectToDeviceV2()
Future<bool> isDeviceConnectedV2()
Future<List<Map<String, dynamic>>> startScanDevicesV2()

// Data retrieval (v2 methods using BleSDK_J2301A/blesdk2301)
Future<int> getBatteryLevelV2()
Future<String> getHRVDataWithModeV2()
Future<String> getSleepDataV2()
Future<String> getBloodOxygenV2()
Future<String> getTotalActivityDataWithModeV2()
Future<String> getTemperatureDataV2()

// PPG monitoring (v2 methods)
Future<void> startPPGWaveV2()
Future<void> stopPPGWaveV2()
```

### Important Note
- **Use only v2 methods**: These use the current SDKs (BleSDK_J2301A/blesdk2301)
- **Avoid non-v2 methods**: These are deprecated and use old SDKs
- **Method naming**: v2 suffix indicates current implementation

### Data Flow (v2 Implementation)
1. **Flutter** calls v2 method via MethodChannel
2. **iOS**: BleServiceV2 uses BleSDK_J2301A for processing
3. **Android**: MainActivity uses blesdk2301 for processing
4. **Ring device** responds with raw BLE data
5. **SDK parsing**: BleSDK_J2301A/blesdk2301 converts raw data to readable format
6. **Flutter** receives formatted data and updates UI

---

## Common Issues & Solutions

### Connection Issues
- **Problem**: Ring won't connect
- **Solution**: Check Bluetooth enabled, device nearby, permissions granted
- **Code**: Automatic retry logic handles most connection failures

### Data Sync Issues
- **Problem**: Missing or incomplete data
- **Solution**: Command queue ensures proper data retrieval sequence
- **Code**: Pagination handles large datasets automatically

### Battery Optimization
- **Problem**: Connection drops in background
- **Solution**: Battery optimization whitelist request
- **Code**: Implemented in Android notification service

---

## Testing & Debugging

### iOS Debug Logs
- Search for `[BleServiceV2]` in Xcode console
- YCProductSDK logs appear with `YCProduct` prefix

### Android Debug Logs
- Search for `====== RING DEBUG ======` in Android Studio logcat
- Filter by `MainActivity`, `BleManager`, `BleService` tags

### Common Debug Commands
```bash
# iOS Simulator
xcrun simctl list devices

# Android Debug
adb logcat | grep "RING DEBUG"
```

---

## Maintenance Notes

### iOS (v2 Implementation)
- **BleSDK_J2301A**: Current SDK - monitor for updates for new ring models
- **BleServiceV2**: Main implementation - handles all v2 method calls
- **Legacy cleanup**: Remove old YCProductSDK references if found
- Monitor iOS Bluetooth permission changes

### Android (v2 Implementation)
- **blesdk2301**: Current SDK - ensure compatibility with new Android versions
- **v2 methods**: All current functionality uses these methods
- **Legacy cleanup**: Remove old non-v2 method implementations if found
- BLE scanning behavior changes with Android versions
- Location permissions required for BLE scanning
- Test on various Android API levels

### Both Platforms
- **SDK Updates**: BleSDK_J2301A (iOS) and blesdk2301 (Android) may need updates for new ring firmware
- **v2 Method Consistency**: Ensure both platforms implement same v2 methods
- **Legacy Code**: Remove any non-v2 method references
- Monitor BLE characteristic changes
- Test connection stability across app lifecycle events
