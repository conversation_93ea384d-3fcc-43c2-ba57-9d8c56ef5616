# Ring Connection - Complete Handover Documentation

## Overview
The Ring Connection feature enables the Flutter app to connect to smart ring devices via Bluetooth Low Energy (BLE) to collect health data including heart rate, steps, sleep patterns, SpO2, and battery information.

---

## iOS Implementation

### How It Works
- **Primary SDK**: YCProductSDK.framework (main connection and data handling)
- **Backup SDK**: BleServiceV2.swift (custom implementation using BleSDK_J2301A)
- **Connection Method**: Bluetooth Low Energy (BLE) with automatic fallback
- **Architecture**: Dual-mode system with primary and backup services

### Key Components

#### 1. YCProductSDK (Primary)
- **Location**: `ios/Runner/YCProductSDK.framework/`
- **Main Class**: `YCProduct`
- **Key Methods**:
  - `YCProduct.connectDevice(peripheral, completion)`
  - `YCProduct.disconnectDevice(peripheral, completion)`
  - `YCProduct.scanningDevice(delayTime, completion)`
  - `YCProduct.queryDeviceBasicInfo(peripheral, completion)`

#### 2. BleServiceV2 (Backup)
- **Location**: `ios/Runner/BleServiceV2.swift`
- **Purpose**: Custom BLE implementation when YCProductSDK fails
- **Features**: Command queue, timeout handling, data parsing

### BLE Characteristics & Services

#### Service UUID
- **Primary Service**: `FFF0`

#### Characteristics
- **Send Data (App → Ring)**: `FFF6`
- **Receive Data (Ring → App)**: `FFF7`
- **Notification Descriptor**: `2902`

### SDK v2 Methods Available

#### Connection Methods
```swift
// Connect to device
func connectToDevice(uuidString: String, completion: @escaping (Bool, Error?) -> Void)

// Disconnect device
func disconnectToDevice(completion: ((Bool, Error?) -> Void)?)

// Check connection status
func isConnected() -> Bool

// Start scanning
func startScanning(completion: @escaping ([CBPeripheral]?, Error?) -> Void)
```

#### Data Retrieval Methods
```swift
// Battery level
callData(type: "batteryLevel", completion: @escaping (String) -> Void)

// Device information
callData(type: "version", completion: @escaping (String) -> Void)
callData(type: "getDeviceMac", completion: @escaping (String) -> Void)

// Health data
callData(type: "getHRVDataWithMode", completion: @escaping (String) -> Void)
callData(type: "getSleepData", completion: @escaping (String) -> Void)
callData(type: "getBloodOxygen", completion: @escaping (String) -> Void)
callData(type: "totalActivityDataWithMode", completion: @escaping (String) -> Void)
callData(type: "getTemperatureData", completion: @escaping (String) -> Void)

// PPG monitoring
callData(type: "startPPGWave", completion: @escaping (String) -> Void)
callData(type: "stopPPGWave", completion: @escaping (String) -> Void)
```

### Permissions Required (Info.plist)
```xml
<key>NSBluetoothAlwaysUsageDescription</key>
<string>App uses Bluetooth to connect to ring device for health monitoring</string>

<key>UIBackgroundModes</key>
<array>
    <string>bluetooth-central</string>
</array>
```

### Special Features Added

#### 1. Command Queue System
- **Purpose**: Prevents command conflicts by processing one command at a time
- **Implementation**: Queue-based command processing with 15-second timeout
- **Location**: `BleServiceV2.swift` lines 65-67

#### 2. Automatic Retry Logic
- **Purpose**: Handles connection failures gracefully
- **Implementation**: Automatic reconnection attempts with exponential backoff
- **Location**: `RingConnectivity.swift`

#### 3. Dual-Mode Operation
- **Primary**: YCProductSDK for standard operations
- **Fallback**: BleServiceV2 when primary SDK fails
- **Switching Logic**: Automatic detection and fallback

#### 4. Data Pagination
- **Purpose**: Handles large data sets (activity, sleep data)
- **Implementation**: Automatic next-page requests when data count reaches 50
- **Location**: `BleServiceV2.swift` lines 606-624

---

## Android Implementation

### How It Works
- **Custom BLE Service**: Direct Bluetooth GATT implementation
- **SDK Integration**: Uses `com.jstyle.blesdk2301` for data parsing
- **Connection Method**: BluetoothGatt with priority optimization
- **Architecture**: Service-based with priority connection handling

### Key Components

#### 1. BleManager
- **Location**: `android/app/src/main/java/com/saigeware/sh/saiwell/BleManager.java`
- **Purpose**: Main Bluetooth management and device scanning
- **Key Features**: Priority connection, device filtering, scan optimization

#### 2. BleService
- **Location**: `android/app/src/main/java/com/saigeware/sh/saiwell/BleService.java`
- **Purpose**: Low-level GATT operations and data handling
- **Key Features**: Connection management, characteristic notifications

#### 3. MainActivity
- **Location**: `android/app/src/main/java/com/saigeware/sh/saiwell/MainActivity.java`
- **Purpose**: Flutter method channel bridge
- **Key Features**: Command queue, data accumulation, PPG streaming

### BLE Characteristics & Services

#### Service UUID
- **Primary Service**: `0000fff0-0000-1000-8000-00805f9b34fb`

#### Characteristics
- **Send Data**: `0000fff6-0000-1000-8000-00805f9b34fb`
- **Receive Data**: `0000fff7-0000-1000-8000-00805f9b34fb`
- **Notification Descriptor**: `00002902-0000-1000-8000-00805f9b34fb`

### SDK v2 Methods Available

#### Connection Methods
```java
// Connect to device
connectToDeviceV2(String macId, Result result)

// Disconnect device
disconnectToDeviceV2(Result result)

// Check connection status
isDeviceConnectedV2(Result result)

// Start scanning
startScanDevicesV2(Result result)
```

#### Data Retrieval Methods
```java
// Battery level
getBatteryLevelV2(Result result)

// Health data
getHRVDataWithModeV2(Result result)
getSleepDataV2(Result result)
getBloodOxygenV2(Result result)
getTotalActivityDataWithModeV2(Result result)
getTemperatureDataV2(Result result)

// PPG monitoring
startPPGWaveV2(Result result)
stopPPGWaveV2(Result result)
```

### Permissions Required (AndroidManifest.xml)
```xml
<!-- Core Bluetooth permissions -->
<uses-permission android:name="android.permission.BLUETOOTH" />
<uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
<uses-permission android:name="android.permission.BLUETOOTH_SCAN" android:usesPermissionFlags="neverForLocation" />
<uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />

<!-- Location permissions (required for BLE scanning) -->
<uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"/>
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/>

<!-- Hardware feature -->
<uses-feature android:name="android.hardware.bluetooth_le" android:required="true" />
```

### Special Features Added

#### 1. Priority Connection System
- **Purpose**: Faster connection for bonded devices
- **Implementation**: Uses `TRANSPORT_LE` for API 23+ with bonded device detection
- **Location**: `BleService.java` lines 124-162

#### 2. Smart Device Filtering
- **Purpose**: Only shows ring devices (filters by name "2301")
- **Implementation**: Device name filtering during scan
- **Location**: `BleManager.java` lines 376-390

#### 3. Command Queue System
- **Purpose**: Prevents data conflicts by queuing commands
- **Implementation**: HashMap-based pending results management
- **Location**: `MainActivity.java` lines 51-53

#### 4. Auto-Retry Logic
- **Purpose**: Multiple connection attempts on failure
- **Implementation**: Retry counter with connection state management
- **Location**: `MainActivity.java` connection callbacks

#### 5. API Level Compatibility
- **Purpose**: Works on Android 5.0+ (API 21+) with optimizations for newer versions
- **Implementation**: Version-specific scan settings and connection methods
- **Location**: `BleManager.java` lines 254-267

---

## Flutter Integration

### Method Channel
- **Channel Name**: `saiwell_native`
- **Implementation**: `lib/services/native_communicator.dart`

### Available Methods
```dart
// Connection
Future<void> connectToDeviceV2({required String macId, ...})
Future<void> disconnectToDeviceV2()
Future<bool> isDeviceConnectedV2()
Future<List<Map<String, dynamic>>> startScanDevicesV2()

// Data retrieval
Future<int> getBatteryLevelV2()
Future<String> getHRVDataWithModeV2()
Future<String> getSleepDataV2()
Future<String> getBloodOxygenV2()
Future<String> getTotalActivityDataWithModeV2()
Future<String> getTemperatureDataV2()

// PPG monitoring
Future<void> startPPGWaveV2()
Future<void> stopPPGWaveV2()
```

### Data Flow
1. **Flutter** calls native method via MethodChannel
2. **Native platform** processes request using appropriate SDK
3. **Ring device** responds with data
4. **Native platform** parses and formats data
5. **Flutter** receives formatted data and updates UI

---

## Common Issues & Solutions

### Connection Issues
- **Problem**: Ring won't connect
- **Solution**: Check Bluetooth enabled, device nearby, permissions granted
- **Code**: Automatic retry logic handles most connection failures

### Data Sync Issues
- **Problem**: Missing or incomplete data
- **Solution**: Command queue ensures proper data retrieval sequence
- **Code**: Pagination handles large datasets automatically

### Battery Optimization
- **Problem**: Connection drops in background
- **Solution**: Battery optimization whitelist request
- **Code**: Implemented in Android notification service

---

## Testing & Debugging

### iOS Debug Logs
- Search for `[BleServiceV2]` in Xcode console
- YCProductSDK logs appear with `YCProduct` prefix

### Android Debug Logs
- Search for `====== RING DEBUG ======` in Android Studio logcat
- Filter by `MainActivity`, `BleManager`, `BleService` tags

### Common Debug Commands
```bash
# iOS Simulator
xcrun simctl list devices

# Android Debug
adb logcat | grep "RING DEBUG"
```

---

## Maintenance Notes

### iOS
- YCProductSDK.framework may need updates for new ring models
- BleServiceV2 serves as reliable fallback
- Monitor iOS Bluetooth permission changes

### Android
- BLE scanning behavior changes with Android versions
- Location permissions required for BLE scanning
- Test on various Android API levels

### Both Platforms
- Ring firmware updates may require SDK updates
- Monitor BLE characteristic changes
- Test connection stability across app lifecycle events
